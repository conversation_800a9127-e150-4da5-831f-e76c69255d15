/*!
# 💬 Chat Port Client Interactive Demo

A beautiful interactive demonstration of the Wellbot Chat Port Client service using cliclack.
This example showcases the chat port client's WebSocket capabilities with a modern, user-friendly CLI interface.

## Features
- ✨ Beautiful interactive prompts with colors and emojis
- 🔌 Real-time WebSocket connection management
- 📱 WhatsApp message sending and receiving
- 🎨 Styled output with comprehensive message formatting
- 🔧 Configuration management (default, custom, environment)
- 🚀 Background task support with tokio::spawn
- 📊 Connection monitoring and health checks
- 🔄 Automatic reconnection handling
- 🎯 Error handling with helpful troubleshooting tips
- 💬 Interactive message composition and sending
- 📋 Message history and real-time updates

## Usage
```bash
cargo run --example chat_port_client_demo --features examples
```

## Prerequisites
- Chat-port service running (typically on localhost:8081)
- WebSocket endpoint available at ws://localhost:8081/ws
- HTTP API endpoint available at http://localhost:8081/api
*/

use cliclack::{confirm, input, intro, log, note, outro, outro_cancel, select, spinner};
use console::style;
use std::{
    sync::{Arc, Mutex},
    thread,
    time::Duration,
};
use tokio::sync::mpsc;
use wellbot_bridge::{
    config::{ChatPortConfig, Config},
    services::chat_port_client::ChatPortClient,
    types::{IncomingWhatsAppData, SendMessageRequest},
};

/// Background task execution mode
#[derive(Debug, Clone, Copy)]
enum BackgroundMode {
    /// Always run tasks in background using tokio::spawn
    Always,
    /// Never run tasks in background, execute synchronously
    Never,
    /// Ask user for each operation
    Ask,
}

/// Message history for display
#[derive(Debug, Clone)]
struct MessageHistory {
    messages: Arc<Mutex<Vec<DisplayMessage>>>,
}

#[derive(Debug, Clone)]
struct DisplayMessage {
    timestamp: chrono::DateTime<chrono::Utc>,
    from: String,
    content: String,
    message_type: MessageType,
}

#[derive(Debug, Clone)]
enum MessageType {
    Incoming,
    Outgoing,
    System,
    Error,
}

impl MessageHistory {
    fn new() -> Self {
        Self {
            messages: Arc::new(Mutex::new(Vec::new())),
        }
    }

    fn add_message(&self, from: String, content: String, message_type: MessageType) {
        if let Ok(mut messages) = self.messages.lock() {
            messages.push(DisplayMessage {
                timestamp: chrono::Utc::now(),
                from,
                content,
                message_type,
            });

            // Keep only last 50 messages to prevent memory issues
            if messages.len() > 50 {
                let excess = messages.len() - 50;
                messages.drain(0..excess);
            }
        }
    }

    fn get_recent_messages(&self, count: usize) -> Vec<DisplayMessage> {
        if let Ok(messages) = self.messages.lock() {
            messages.iter().rev().take(count).cloned().collect()
        } else {
            Vec::new()
        }
    }
}

#[tokio::main]
async fn main() -> std::io::Result<()> {
    // Set up Ctrl-C handler for graceful exit
    ctrlc::set_handler(move || {
        println!("\n🛑 Received interrupt signal - cleaning up...");
        std::process::exit(0);
    })
    .expect("setting Ctrl-C handler");

    // Clear screen and show beautiful intro
    cliclack::clear_screen()?;

    intro(
        style(" 💬 Wellbot Chat Port Client Demo ")
            .on_blue()
            .white(),
    )?;

    log::info("Welcome to the Wellbot Chat Port Client interactive demonstration!")?;
    log::remark("This demo showcases the chat-port WebSocket integration capabilities")?;

    // Service requirement notice
    note(
        "📋 Prerequisites",
        "This demo requires the Chat-port service to be running.\nThe service should be available at:\n• WebSocket: ws://localhost:8081/ws\n• HTTP API: http://localhost:8081/api",
    )?;

    // Check if user wants to start the service automatically
    let mut spawned_service = None;
    if confirm("🚀 Would you like to automatically start the chat-port service using tokio::spawn?")
        .initial_value(true)
        .interact()?
    {
        spawned_service = spawn_chat_port_service().await?;
    } else {
        note(
            "💡 Manual Service Startup",
            "To start the service manually, run this command in a separate terminal:\n\n  cd chat-port\n  go run cmd/main.go",
        )?;
    }

    // Configuration setup
    let config_choice = select("How would you like to configure the chat-port service?")
        .initial_value("default")
        .item(
            "default",
            "Use default configuration",
            "Recommended for testing (localhost:8081)",
        )
        .item("custom", "Custom configuration", "Specify custom URLs")
        .item("env", "Load from environment", "Production-like setup")
        .interact()?;

    let config = match config_choice {
        "custom" => create_custom_config().await?,
        "env" => {
            log::step("Loading configuration from environment variables...")?;
            match Config::from_env() {
                Ok(config) => {
                    log::success("Configuration loaded successfully!")?;
                    config
                }
                Err(e) => {
                    log::warning(format!("Failed to load from env: {}", e))?;
                    log::remark("Falling back to default configuration")?;
                    Config::default()
                }
            }
        }
        _ => {
            log::step("Using default configuration")?;
            Config::default()
        }
    };

    // Display configuration summary
    display_config_summary(&config.chat_port)?;

    // Initialize chat port client
    let spinner = spinner();
    spinner.start("🔧 Initializing chat port client...");
    thread::sleep(Duration::from_millis(800)); // Simulate initialization

    let chat_client = match ChatPortClient::new(config.chat_port.clone()) {
        Ok(client) => {
            spinner.stop("✅ Chat port client initialized successfully!");
            client
        }
        Err(e) => {
            spinner.stop("❌ Failed to initialize chat port client");
            log::error(format!("Initialization error: {}", e))?;
            outro_cancel("Demo terminated due to initialization failure")?;
            return Ok(());
        }
    };

    // Connection test
    if confirm("🔌 Would you like to test the connection to chat-port service?").interact()? {
        match test_connection(&chat_client).await {
            Ok(_) => {}
            Err(_) => {
                log::warning("❌ Connection test failed - Chat-port service may not be running")?;
                note(
                    "🚀 Start the Chat-port Service",
                    "To start the chat-port service, run this command in a separate terminal:\n\n  cd chat-port\n  go run cmd/main.go\n\nThen restart this demo to connect to the service.",
                )?;

                if !confirm("🔄 Continue with demo anyway? (some features may not work)")
                    .initial_value(false)
                    .interact()?
                {
                    outro_cancel("Demo cancelled - please start the chat-port service first")?;
                    return Ok(());
                }
            }
        }
    }

    // Background task execution preference
    let use_background_tasks = select("🚀 Background Task Execution Mode")
        .initial_value("interactive")
        .item(
            "interactive",
            "🎯 Interactive Mode",
            "Execute tasks synchronously with real-time feedback",
        )
        .item(
            "background",
            "⚡ Background Mode",
            "Use tokio::spawn for concurrent execution",
        )
        .item(
            "hybrid",
            "🔄 Hybrid Mode",
            "Ask for each operation individually",
        )
        .interact()?;

    let background_preference = match use_background_tasks {
        "background" => {
            log::success(
                "🚀 Background mode enabled - Tasks will run concurrently using tokio::spawn",
            )?;
            note(
                "⚡ Background Mode Benefits",
                "• Non-blocking WebSocket listening\n• Concurrent message processing\n• Real-time message updates\n• Better performance for multiple operations",
            )?;
            BackgroundMode::Always
        }
        "hybrid" => {
            log::info("🔄 Hybrid mode enabled - You'll be asked for each operation")?;
            note(
                "🔄 Hybrid Mode Features",
                "• Choose execution mode per operation\n• Flexibility for different use cases\n• Learn the differences interactively",
            )?;
            BackgroundMode::Ask
        }
        _ => {
            log::info("🎯 Interactive mode enabled - Tasks will run synchronously")?;
            note(
                "🎯 Interactive Mode Features",
                "• Sequential execution\n• Immediate feedback\n• Simple and predictable\n• Perfect for learning",
            )?;
            BackgroundMode::Never
        }
    };

    // Initialize message history
    let message_history = MessageHistory::new();

    // Main demo loop
    loop {
        let action = select("What would you like to do?")
            .initial_value("listen")
            .item(
                "listen",
                "👂 Listen for Messages",
                "Start WebSocket listener for incoming messages",
            )
            .item(
                "send",
                "📤 Send WhatsApp Message",
                "Send a message via HTTP API",
            )
            .item(
                "interactive",
                "💬 Interactive Chat Mode",
                "Real-time chat with message history",
            )
            .item(
                "test",
                "🔬 Test Connection",
                "Verify chat-port service connectivity",
            )
            .item(
                "history",
                "📋 View Message History",
                "Display recent messages",
            )
            .item(
                "config",
                "⚙️  View Configuration",
                "Display current settings",
            )
            .item("exit", "🚪 Exit Demo", "End the demonstration")
            .interact()?;

        match action {
            "listen" => {
                run_with_background_preference(
                    background_preference,
                    "message listening",
                    &chat_client,
                    &message_history,
                    "listen",
                )
                .await?
            }
            "send" => {
                run_with_background_preference(
                    background_preference,
                    "message sending",
                    &chat_client,
                    &message_history,
                    "send",
                )
                .await?
            }
            "interactive" => {
                run_with_background_preference(
                    background_preference,
                    "interactive chat",
                    &chat_client,
                    &message_history,
                    "interactive",
                )
                .await?
            }
            "test" => {
                run_with_background_preference(
                    background_preference,
                    "connection test",
                    &chat_client,
                    &message_history,
                    "test",
                )
                .await?
            }
            "history" => display_message_history(&message_history)?,
            "config" => display_config_summary(&config.chat_port)?,
            "exit" => break,
            _ => log::warning("Unknown action selected")?,
        }

        if !confirm("🔄 Continue with another action?").interact()? {
            break;
        }
    }

    // Cleanup: Kill spawned chat-port service if it was started
    cleanup_spawned_service(spawned_service).await?;

    outro(format!(
        "🎉 Thanks for trying the Wellbot Chat Port Client Demo!\n\n{}",
        style("For more information, visit: https://github.com/wellbot/wellbot")
            .cyan()
            .underlined()
    ))?;

    Ok(())
}

/// Create custom configuration interactively
async fn create_custom_config() -> std::io::Result<Config> {
    log::step("🔧 Creating custom configuration")?;

    let websocket_url: String = input("WebSocket URL")
        .placeholder("ws://localhost:8081/ws")
        .validate(|input: &String| {
            if input.trim().is_empty() {
                Err("WebSocket URL cannot be empty")
            } else if !input.starts_with("ws://") && !input.starts_with("wss://") {
                Err("WebSocket URL must start with ws:// or wss://")
            } else {
                Ok(())
            }
        })
        .interact()?;

    let api_base_url: String = input("HTTP API Base URL")
        .placeholder("http://localhost:8081/api")
        .validate(|input: &String| {
            if input.trim().is_empty() {
                Err("API Base URL cannot be empty")
            } else if !input.starts_with("http://") && !input.starts_with("https://") {
                Err("API Base URL must start with http:// or https://")
            } else {
                Ok(())
            }
        })
        .interact()?;

    let mut config = Config::default();
    config.chat_port.websocket_url = websocket_url;
    config.chat_port.api_base_url = api_base_url;

    log::success("Custom configuration created successfully!")?;
    Ok(config)
}

/// Display configuration summary
fn display_config_summary(config: &ChatPortConfig) -> std::io::Result<()> {
    let config_info = format!(
        "🌐 WebSocket URL: {}\n🔗 API Base URL: {}\n⏱️  Connection Timeout: {}s\n🔄 Reconnect Delay: {}s\n🔁 Max Reconnect Attempts: {}\n💓 Heartbeat Interval: {}s",
        style(&config.websocket_url).cyan(),
        style(&config.api_base_url).cyan(),
        style(config.connection_timeout_secs).green(),
        style(config.reconnect_delay_secs).green(),
        style(config.max_reconnect_attempts).green(),
        style(config.heartbeat_interval_secs).green()
    );

    note("⚙️ Chat Port Configuration", config_info)?;
    Ok(())
}

/// Spawn the chat-port service in the background using tokio::spawn
async fn spawn_chat_port_service() -> std::io::Result<Option<u32>> {
    log::step("🚀 Starting chat-port service in background...")?;

    let spinner = spinner();
    spinner.start("⚡ Spawning chat-port service using tokio::spawn...");

    // Check if we're in the correct directory structure
    let chat_port_path = std::path::Path::new("../chat-port");
    if !chat_port_path.exists() {
        spinner.stop("❌ Failed to locate chat-port directory");
        log::error(
            "Could not find the chat-port directory. Please ensure you're running from the core directory.",
        )?;
        return Err(std::io::Error::new(
            std::io::ErrorKind::NotFound,
            "chat-port directory not found",
        ));
    }

    // Spawn the service in background
    let mut child = tokio::process::Command::new("go")
        .arg("run")
        .arg("cmd/main.go")
        .current_dir("../chat-port")
        .stdout(std::process::Stdio::null())
        .stderr(std::process::Stdio::null())
        .spawn()
        .map_err(|e| {
            log::error(format!("Failed to start chat-port service: {}", e)).ok();
            e
        })?;

    let process_id = child.id();
    log::info("🎯 Chat-port service process started successfully")?;

    // Let the service start up in background
    tokio::spawn(async move {
        tokio::time::sleep(tokio::time::Duration::from_secs(3)).await;

        // Check if process is still running
        match child.try_wait() {
            Ok(Some(status)) => {
                log::warning(format!(
                    "⚠️ Chat-port service exited with status: {}",
                    status
                ))
                .ok();
            }
            Ok(None) => {
                log::success("✅ Chat-port service is running in background").ok();
            }
            Err(e) => {
                log::error(format!("Failed to check service status: {}", e)).ok();
            }
        }
    });

    spinner.stop("✅ Chat-port service started successfully!");
    log::success("🎉 The chat-port service is now running in the background")?;
    log::remark("Service available at: http://localhost:8081")?;

    Ok(process_id)
}

/// Execute a task with the user's background preference
async fn run_with_background_preference(
    preference: BackgroundMode,
    task_name: &str,
    chat_client: &ChatPortClient,
    message_history: &MessageHistory,
    task_type: &str,
) -> std::io::Result<()> {
    match task_type {
        "listen" => match preference {
            BackgroundMode::Always => {
                log::info(format!(
                    "🚀 Running {} in background using tokio::spawn",
                    task_name
                ))?;
                note(
                    "⚡ Background Mode",
                    "WebSocket listening will run concurrently in the background",
                )?;
                listen_for_messages_demo(chat_client, message_history).await
            }
            BackgroundMode::Ask => {
                let use_background = confirm(format!(
                    "🤔 Run {} in background using tokio::spawn?",
                    task_name
                ))
                .interact()?;

                if use_background {
                    log::info(format!("🚀 Running {} in background", task_name))?;
                    note(
                        "⚡ Background Mode",
                        "WebSocket listening will run concurrently in the background",
                    )?;
                } else {
                    log::info(format!("🎯 Running {} synchronously", task_name))?;
                }
                listen_for_messages_demo(chat_client, message_history).await
            }
            BackgroundMode::Never => {
                log::info(format!("🎯 Running {} synchronously", task_name))?;
                listen_for_messages_demo(chat_client, message_history).await
            }
        },
        "send" => {
            log::info(format!("📤 Running {} task", task_name))?;
            send_message_demo(chat_client, message_history).await
        }
        "interactive" => {
            log::info(format!("💬 Running {} task", task_name))?;
            interactive_chat_demo(chat_client, message_history).await
        }
        "test" => {
            log::info(format!("🔬 Running {} task", task_name))?;
            test_connection(chat_client).await
        }
        _ => {
            log::error(format!("Unknown task type: {}", task_type))?;
            Err(std::io::Error::new(
                std::io::ErrorKind::InvalidInput,
                "Unknown task type",
            ))
        }
    }
}

/// Test connection to chat-port service
async fn test_connection(chat_client: &ChatPortClient) -> std::io::Result<()> {
    log::step("🔬 Testing chat-port service connection")?;

    let spinner = spinner();
    spinner.start("🔗 Testing connection to chat-port service...");

    // Test connection by making a simple HTTP request to the health endpoint
    let health_url = format!("{}/health", chat_client.config.api_base_url);

    match reqwest::Client::new()
        .get(&health_url)
        .timeout(std::time::Duration::from_secs(10))
        .send()
        .await
    {
        Ok(response) => {
            if response.status().is_success() {
                spinner.stop("✅ Connection test successful!");
                log::success("Chat-port service is reachable and responding correctly")?;

                // Try to parse the health response to show more details
                if let Ok(health_text) = response.text().await {
                    if let Ok(health_json) = serde_json::from_str::<serde_json::Value>(&health_text)
                    {
                        if let Some(services) = health_json.get("services") {
                            if let Some(whatsapp) = services.get("whatsapp") {
                                if let Some(connected) = whatsapp.get("connected") {
                                    if connected.as_bool() == Some(true) {
                                        if let Some(jid) = whatsapp.get("jid") {
                                            log::success(format!(
                                                "WhatsApp client connected with JID: {}",
                                                jid
                                            ))?;
                                        }
                                    } else {
                                        log::warning("WhatsApp client is not connected")?;
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                spinner.stop("❌ Connection test failed");
                log::error(format!(
                    "Health endpoint returned status: {}",
                    response.status()
                ))?;
                return Err(std::io::Error::new(
                    std::io::ErrorKind::ConnectionRefused,
                    "Health check failed",
                ));
            }
        }
        Err(e) => {
            spinner.stop("❌ Connection test failed");
            log::error(format!("Connection error: {}", e))?;

            // Provide helpful troubleshooting tips
            note(
                "🔧 Troubleshooting Tips",
                "• Start the chat-port service: cd chat-port && go run cmd/main.go\n• Check if the service is running on the correct port\n• Verify the API base URL is correct\n• Check network connectivity\n• Review firewall settings\n• Ensure WhatsApp client is connected",
            )?;

            return Err(std::io::Error::new(
                std::io::ErrorKind::ConnectionRefused,
                "Connection test failed",
            ));
        }
    }

    Ok(())
}

/// Listen for incoming messages demonstration
async fn listen_for_messages_demo(
    chat_client: &ChatPortClient,
    message_history: &MessageHistory,
) -> std::io::Result<()> {
    log::step("👂 Starting WebSocket message listener")?;

    let duration_choice = select("How long would you like to listen for messages?")
        .initial_value("30s")
        .item("10s", "10 seconds", "Quick test")
        .item("30s", "30 seconds", "Short demo")
        .item("60s", "1 minute", "Extended demo")
        .item("300s", "5 minutes", "Long session")
        .item("continuous", "Continuous", "Listen until manually stopped")
        .interact()?;

    let duration = match duration_choice {
        "10s" => Some(Duration::from_secs(10)),
        "30s" => Some(Duration::from_secs(30)),
        "60s" => Some(Duration::from_secs(60)),
        "300s" => Some(Duration::from_secs(300)),
        _ => None,
    };

    let spinner = spinner();
    if let Some(dur) = duration {
        spinner.start(format!(
            "🔌 Connecting to WebSocket and listening for {}s...",
            dur.as_secs()
        ));
    } else {
        spinner.start("🔌 Connecting to WebSocket and listening continuously...");
    }

    // Create a channel for message communication
    let (tx, mut rx) = mpsc::unbounded_channel::<IncomingWhatsAppData>();
    let history = message_history.clone();

    // Create message handler
    let message_handler = move |data: IncomingWhatsAppData| {
        let tx = tx.clone();
        let history = history.clone();
        async move {
            // Add to history
            history.add_message(
                data.from.clone(),
                data.message.clone(),
                MessageType::Incoming,
            );

            // Send to channel for display
            if let Err(e) = tx.send(data) {
                log::error(format!("Failed to send message to display channel: {}", e)).ok();
            }
            Ok(())
        }
    };

    // Start listening in background
    let client = chat_client.clone();
    let listen_handle =
        tokio::spawn(async move { client.listen_for_messages(message_handler).await });

    spinner.stop("✅ WebSocket connected - listening for messages...");
    log::success("🎉 Now listening for incoming WhatsApp messages!")?;
    log::remark("Send a WhatsApp message to see it appear here in real-time")?;

    let mut message_count = 0;
    let start_time = std::time::Instant::now();

    // Listen for messages with optional timeout
    let result = if let Some(timeout_duration) = duration {
        tokio::select! {
            _ = tokio::time::sleep(timeout_duration) => {
                log::info(format!("⏰ Listening timeout reached ({}s)", timeout_duration.as_secs()))?;
                Ok(())
            }
            result = async {
                while let Some(message) = rx.recv().await {
                    message_count += 1;
                    display_incoming_message(&message, message_count)?;
                }
                Ok::<(), std::io::Error>(())
            } => result
        }
    } else {
        // Continuous listening until user interrupts
        log::info("Press Ctrl+C to stop listening")?;
        while let Some(message) = rx.recv().await {
            message_count += 1;
            display_incoming_message(&message, message_count)?;
        }
        Ok(())
    };

    // Stop the listening task
    listen_handle.abort();

    let elapsed = start_time.elapsed();
    let summary = format!(
        "📊 Listening Session Summary:\n📨 Messages Received: {}\n⏱️ Duration: {:.1}s\n📈 Average Rate: {:.2} messages/min",
        style(message_count).green(),
        style(elapsed.as_secs_f64()).blue(),
        style(message_count as f64 / elapsed.as_secs_f64() * 60.0).yellow()
    );

    note("🎯 Listening Complete", summary)?;

    result
}

/// Display an incoming message with beautiful formatting
fn display_incoming_message(message: &IncomingWhatsAppData, count: usize) -> std::io::Result<()> {
    let timestamp = chrono::Utc::now().format("%H:%M:%S");
    let formatted_message = format!(
        "📨 Message #{} [{}]\n📱 From: {}\n💬 Content: {}\n📊 ID: {}",
        style(count).green().bold(),
        style(timestamp).blue(),
        style(&message.from).cyan().bold(),
        style(&message.message).white(),
        style(&message.message_id).yellow()
    );

    note("📥 Incoming WhatsApp Message", formatted_message)?;
    Ok(())
}

/// Send message demonstration
async fn send_message_demo(
    chat_client: &ChatPortClient,
    message_history: &MessageHistory,
) -> std::io::Result<()> {
    log::step("📤 Sending WhatsApp message via HTTP API")?;

    // Get recipient number
    let number: String = input("📱 Recipient WhatsApp Number")
        .placeholder("+1234567890")
        .validate(|input: &String| {
            if input.trim().is_empty() {
                Err("Phone number cannot be empty")
            } else if !input.starts_with('+') && !input.chars().all(|c| c.is_ascii_digit()) {
                Err("Please enter a valid phone number (with + prefix or digits only)")
            } else {
                Ok(())
            }
        })
        .interact()?;

    // Get message content
    let message: String = input("💬 Message Content")
        .placeholder("Hello from Wellbot Chat Port Client!")
        .validate(|input: &String| {
            if input.trim().is_empty() {
                Err("Message cannot be empty")
            } else if input.len() > 1000 {
                Err("Message too long (max 1000 characters)")
            } else {
                Ok(())
            }
        })
        .interact()?;

    // Confirm before sending
    let confirmation_text = format!(
        "📱 To: {}\n💬 Message: {}\n📏 Length: {} characters",
        style(&number).cyan(),
        style(&message).white(),
        style(message.len()).green()
    );

    note("📋 Message Preview", confirmation_text)?;

    if !confirm("📤 Send this message?").interact()? {
        log::info("❌ Message sending cancelled")?;
        return Ok(());
    }

    // Send the message
    let spinner = spinner();
    spinner.start("📤 Sending WhatsApp message...");

    let request = SendMessageRequest {
        number: number.clone(),
        message: message.clone(),
    };

    match chat_client.send_message(request).await {
        Ok(response) => {
            spinner.stop("✅ Message sent successfully!");

            // Add to message history
            message_history.add_message(
                format!("You -> {}", number),
                message.clone(),
                MessageType::Outgoing,
            );

            // Display response details
            let message_id = response.message_id.as_deref().unwrap_or("N/A");
            let response_info = format!(
                "📨 Message ID: {}\n✅ Status: {}\n⏰ Sent at: {}",
                style(message_id).green(),
                style("Delivered").green(),
                style(chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC")).blue()
            );

            note("📊 Send Response", response_info)?;
            log::success("🎉 Your WhatsApp message has been sent successfully!")?;
        }
        Err(e) => {
            spinner.stop("❌ Failed to send message");
            log::error(format!("Send error: {}", e))?;

            // Add error to message history
            message_history.add_message(
                "System".to_string(),
                format!("Failed to send message to {}: {}", number, e),
                MessageType::Error,
            );

            // Provide troubleshooting tips
            note(
                "🔧 Troubleshooting Tips",
                "• Verify the recipient's WhatsApp number is correct\n• Check if the chat-port service is running\n• Ensure WhatsApp Web is connected\n• Verify network connectivity\n• Check service logs for detailed error information",
            )?;

            return Err(std::io::Error::other("Failed to send message"));
        }
    }

    Ok(())
}

/// Interactive chat mode demonstration
async fn interactive_chat_demo(
    chat_client: &ChatPortClient,
    message_history: &MessageHistory,
) -> std::io::Result<()> {
    log::step("💬 Starting interactive chat mode")?;

    note(
        "🎯 Interactive Chat Mode",
        "This mode combines real-time message listening with interactive sending.\nYou can send messages and see incoming messages in real-time.\n\nType 'exit' to leave chat mode.",
    )?;

    // Get the chat partner's number
    let chat_partner: String = input("📱 Chat Partner's WhatsApp Number")
        .placeholder("+1234567890")
        .validate(|input: &String| {
            if input.trim().is_empty() {
                Err("Phone number cannot be empty")
            } else {
                Ok(())
            }
        })
        .interact()?;

    log::success(format!(
        "💬 Starting chat with {}",
        style(&chat_partner).cyan()
    ))?;

    // Start WebSocket listener in background
    let (tx, mut rx) = mpsc::unbounded_channel::<IncomingWhatsAppData>();
    let history = message_history.clone();

    let message_handler = move |data: IncomingWhatsAppData| {
        let tx = tx.clone();
        let history = history.clone();
        async move {
            // Add to history
            history.add_message(
                data.from.clone(),
                data.message.clone(),
                MessageType::Incoming,
            );

            // Send to display channel
            if let Err(e) = tx.send(data) {
                log::error(format!("Failed to send message to display channel: {}", e)).ok();
            }
            Ok(())
        }
    };

    let client = chat_client.clone();
    let _listen_handle =
        tokio::spawn(async move { client.listen_for_messages(message_handler).await });

    // Background task to display incoming messages
    let _display_handle = tokio::spawn(async move {
        while let Some(message) = rx.recv().await {
            if let Err(e) = display_chat_message(&message, false) {
                log::error(format!("Failed to display message: {}", e)).ok();
            }
        }
    });

    log::info("🎉 Chat mode active - you can now send and receive messages!")?;
    log::remark("Type your messages below. Type 'exit' to quit chat mode.")?;

    // Interactive message loop
    loop {
        let user_message: String = input("💬 Your message")
            .placeholder("Type your message here...")
            .interact()?;

        if user_message.trim().to_lowercase() == "exit" {
            log::info("👋 Exiting chat mode")?;
            break;
        }

        if user_message.trim().is_empty() {
            continue;
        }

        // Send the message
        let request = SendMessageRequest {
            number: chat_partner.clone(),
            message: user_message.clone(),
        };

        match chat_client.send_message(request).await {
            Ok(_) => {
                // Add to history and display
                message_history.add_message(
                    format!("You -> {}", chat_partner),
                    user_message.clone(),
                    MessageType::Outgoing,
                );

                // Display sent message
                display_chat_message_sent(&user_message, &chat_partner)?;
            }
            Err(e) => {
                log::error(format!("❌ Failed to send message: {}", e))?;
                message_history.add_message(
                    "System".to_string(),
                    format!("Failed to send: {}", e),
                    MessageType::Error,
                );
            }
        }
    }

    Ok(())
}

/// Display a chat message in interactive mode
fn display_chat_message(message: &IncomingWhatsAppData, _is_outgoing: bool) -> std::io::Result<()> {
    let timestamp = chrono::Utc::now().format("%H:%M:%S");
    println!(
        "📥 [{}] {}: {}",
        style(timestamp).blue(),
        style(&message.from).cyan().bold(),
        style(&message.message).white()
    );
    Ok(())
}

/// Display a sent message in interactive mode
fn display_chat_message_sent(message: &str, recipient: &str) -> std::io::Result<()> {
    let timestamp = chrono::Utc::now().format("%H:%M:%S");
    println!(
        "📤 [{}] You -> {}: {}",
        style(timestamp).blue(),
        style(recipient).cyan().bold(),
        style(message).green()
    );
    Ok(())
}

/// Display message history
fn display_message_history(message_history: &MessageHistory) -> std::io::Result<()> {
    log::step("📋 Displaying message history")?;

    let count_choice = select("How many recent messages would you like to see?")
        .initial_value("10")
        .item("5", "Last 5 messages", "Quick overview")
        .item("10", "Last 10 messages", "Recent activity")
        .item("20", "Last 20 messages", "Extended history")
        .item("all", "All messages", "Complete history")
        .interact()?;

    let count = match count_choice {
        "5" => 5,
        "10" => 10,
        "20" => 20,
        _ => usize::MAX,
    };

    let messages = message_history.get_recent_messages(count);

    if messages.is_empty() {
        log::info("📭 No messages in history yet")?;
        note(
            "💡 Tip",
            "Send some messages or listen for incoming messages to populate the history.",
        )?;
        return Ok(());
    }

    let mut history_display = String::new();
    for (i, msg) in messages.iter().enumerate() {
        let icon = match msg.message_type {
            MessageType::Incoming => "📥",
            MessageType::Outgoing => "📤",
            MessageType::System => "⚙️",
            MessageType::Error => "❌",
        };

        let color = match msg.message_type {
            MessageType::Incoming => style(&msg.content).cyan(),
            MessageType::Outgoing => style(&msg.content).green(),
            MessageType::System => style(&msg.content).blue(),
            MessageType::Error => style(&msg.content).red(),
        };

        history_display.push_str(&format!(
            "{}. {} [{}] {}: {}\n",
            i + 1,
            icon,
            style(msg.timestamp.format("%H:%M:%S")).blue(),
            style(&msg.from).bold(),
            color
        ));
    }

    note(
        format!("📋 Message History ({} messages)", messages.len()),
        history_display.trim(),
    )?;

    Ok(())
}

/// Cleanup spawned chat-port service
async fn cleanup_spawned_service(process_id: Option<u32>) -> std::io::Result<()> {
    if let Some(pid) = process_id {
        log::step("🧹 Cleaning up spawned chat-port service")?;

        let spinner = spinner();
        spinner.start("🛑 Stopping chat-port service...");

        // Try to kill the process gracefully
        #[cfg(windows)]
        {
            let output = tokio::process::Command::new("taskkill")
                .args(["/PID", &pid.to_string(), "/F"])
                .output()
                .await;

            match output {
                Ok(_) => {
                    spinner.stop("✅ Chat-port service stopped successfully");
                    log::success("🎉 Background service cleanup completed")?;
                }
                Err(e) => {
                    spinner.stop("⚠️ Failed to stop service gracefully");
                    log::warning(format!("Service cleanup error: {}", e))?;
                }
            }
        }

        #[cfg(not(windows))]
        {
            let output = tokio::process::Command::new("kill")
                .args(["-TERM", &pid.to_string()])
                .output()
                .await;

            match output {
                Ok(_) => {
                    spinner.stop("✅ Chat-port service stopped successfully");
                    log::success("🎉 Background service cleanup completed")?;
                }
                Err(e) => {
                    spinner.stop("⚠️ Failed to stop service gracefully");
                    log::warning(format!("Service cleanup error: {}", e))?;
                }
            }
        }
    }

    Ok(())
}
