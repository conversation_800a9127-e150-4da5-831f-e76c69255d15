use std::sync::Arc;

use tracing::{info, instrument};

use crate::{
    config::Config,
    error::BridgeResult,
    services::{ai_client::AiClient, chat_port_client::ChatPortClient},
};

/// Main bridge service that orchestrates all components
#[derive(Debug)]
pub struct BridgeService {
    config: Config,
    ai_client: Arc<AiClient>,
    chat_port_client: Arc<ChatPortClient>,
}

impl BridgeService {
    /// Create a new bridge service with all components
    #[instrument(skip(config))]
    pub async fn new(config: Config) -> BridgeResult<Self> {
        info!("Initializing Wellbot Bridge Service components...");

        // Initialize AI client
        let ai_client = Arc::new(AiClient::new(config.ai_service.clone())?);
        info!("✅ AI client initialized");

        // Initialize chat-port client
        let chat_port_client = Arc::new(ChatPortClient::new(config.chat_port.clone())?);
        info!("✅ Chat-port client initialized");

        Ok(Self {
            config,
            ai_client,
            chat_port_client,
        })
    }
}
