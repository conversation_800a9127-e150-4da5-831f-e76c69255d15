use std::time::Duration;

use chrono::Utc;
use futures_util::{SinkExt, stream::StreamExt};
use reqwest::Client as HttpClient;
use tokio::time::{interval, timeout};
use tokio_tungstenite::{connect_async, tungstenite::Message};
use tracing::{debug, error, info, instrument, warn};

use crate::{
    config::ChatPortConfig,
    error::{BridgeError, BridgeResult},
    types::{
        IncomingWhatsAppData, SendMessageRequest, SendMessageResponse, SubscriptionData,
        SubscriptionMessage, WebSocketMessage,
    },
};

/// Chat-port service client
#[derive(Debug, Clone)]
pub struct ChatPortClient {
    config: ChatPortConfig,
    http_client: HttpClient,
}

impl ChatPortClient {
    /// Create a new chat-port client
    pub fn new(config: ChatPortConfig) -> BridgeResult<Self> {
        let http_client = HttpClient::builder()
            .timeout(Duration::from_secs(30))
            .user_agent(format!("wellbot-bridge/{}", env!("CARGO_PKG_VERSION")))
            .build()
            .map_err(BridgeError::Http)?;

        info!(
            "Chat-port client initialized - WebSocket: {}, API: {}",
            config.websocket_url, config.api_base_url
        );

        Ok(Self {
            config,
            http_client,
        })
    }

    /// Connect to chat-port WebSocket and listen for messages
    #[instrument(skip(self, message_handler))]
    pub async fn listen_for_messages<F, Fut>(&self, mut message_handler: F) -> BridgeResult<()>
    where
        F: FnMut(IncomingWhatsAppData) -> Fut + Send,
        Fut: std::future::Future<Output = BridgeResult<()>> + Send,
    {
        let mut reconnect_attempts = 0;
        let max_attempts = self.config.max_reconnect_attempts;

        loop {
            match self.connect_and_listen(&mut message_handler).await {
                Ok(_) => {
                    info!("WebSocket connection closed normally");
                    break;
                }
                Err(e) => {
                    error!("WebSocket connection error: {}", e);

                    if reconnect_attempts >= max_attempts {
                        error!(
                            "Max reconnection attempts ({}) reached, giving up",
                            max_attempts
                        );
                        return Err(e);
                    }

                    reconnect_attempts += 1;
                    let delay = Duration::from_secs(self.config.reconnect_delay_secs);
                    warn!(
                        "Reconnecting in {}s (attempt {}/{})",
                        delay.as_secs(),
                        reconnect_attempts,
                        max_attempts
                    );

                    tokio::time::sleep(delay).await;
                }
            }
        }

        Ok(())
    }

    /// Internal method to handle WebSocket connection and message processing
    async fn connect_and_listen<F, Fut>(&self, message_handler: &mut F) -> BridgeResult<()>
    where
        F: FnMut(IncomingWhatsAppData) -> Fut + Send,
        Fut: std::future::Future<Output = BridgeResult<()>> + Send,
    {
        info!(
            "Connecting to chat-port WebSocket: {}",
            self.config.websocket_url
        );

        // Connect with timeout
        let (ws_stream, _) = timeout(
            Duration::from_secs(self.config.connection_timeout_secs),
            connect_async(&self.config.websocket_url),
        )
        .await
        .map_err(|_| BridgeError::Timeout("WebSocket connection timeout".to_string()))?
        .map_err(BridgeError::WebSocket)?;

        info!("WebSocket connected successfully");

        let (mut ws_sender, mut ws_receiver) = ws_stream.split();

        // Send subscription message
        let subscription = SubscriptionMessage {
            message_type: "subscribe".to_string(),
            timestamp: Utc::now(),
            data: SubscriptionData {
                action: "subscribe".to_string(),
                subscription: "whatsapp".to_string(),
            },
        };

        let subscription_json = serde_json::to_string(&subscription).map_err(BridgeError::Json)?;

        ws_sender
            .send(Message::Text(subscription_json.into()))
            .await
            .map_err(BridgeError::WebSocket)?;

        info!("Subscribed to WhatsApp messages");

        // Set up heartbeat
        let mut heartbeat_interval =
            interval(Duration::from_secs(self.config.heartbeat_interval_secs));

        loop {
            tokio::select! {
                // Handle incoming messages
                message = ws_receiver.next() => {
                    match message {
                        Some(Ok(Message::Text(text))) => {
                            debug!("Received WebSocket message: {}", text);

                            match serde_json::from_str::<WebSocketMessage>(&text) {
                                Ok(WebSocketMessage::IncomingWhatsApp { data, .. }) => {
                                    info!(
                                        "Received WhatsApp message from {}: {}",
                                        data.from,
                                        data.message.chars().take(50).collect::<String>()
                                    );

                                    if let Err(e) = message_handler(data).await {
                                        error!("Message handler error: {}", e);
                                    }
                                }
                                Ok(WebSocketMessage::Welcome { data, .. }) => {
                                    info!("Received welcome message - Client ID: {}", data.client_id);
                                }
                                Ok(WebSocketMessage::StatusUpdate { data, .. }) => {
                                    debug!("Status update: {} is {}", data.service, data.status);
                                }
                                Ok(WebSocketMessage::Error { error, .. }) => {
                                    warn!("Received error message: {}", error);
                                }
                                Ok(WebSocketMessage::Ping { .. }) => {
                                    debug!("Received ping, sending pong");
                                    let pong = serde_json::json!({
                                        "type": "pong",
                                        "timestamp": Utc::now()
                                    });
                                    if let Err(e) = ws_sender.send(Message::Text(pong.to_string().into())).await {
                                        error!("Failed to send pong: {}", e);
                                    }
                                }
                                Ok(WebSocketMessage::Pong { .. }) => {
                                    debug!("Received pong");
                                }
                                Err(e) => {
                                    warn!("Failed to parse WebSocket message: {} - Raw: {}", e, text);
                                }
                            }
                        }
                        Some(Ok(Message::Close(_))) => {
                            info!("WebSocket connection closed by server");
                            break;
                        }
                        Some(Err(e)) => {
                            error!("WebSocket error: {}", e);
                            return Err(Box::new(BridgeError::WebSocket(e)));
                        }
                        None => {
                            warn!("WebSocket stream ended");
                            break;
                        }
                        _ => {
                            debug!("Received non-text WebSocket message");
                        }
                    }
                }

                // Send heartbeat
                _ = heartbeat_interval.tick() => {
                    debug!("Sending heartbeat");
                    let heartbeat = serde_json::json!({
                        "type": "heartbeat",
                        "timestamp": Utc::now()
                    });

                    if let Err(e) = ws_sender.send(Message::Text(heartbeat.to_string().into())).await {
                        error!("Failed to send heartbeat: {}", e);
                        return Err(Box::new(BridgeError::WebSocket(e)));
                    }
                }
            }
        }

        Ok(())
    }

    /// Send WhatsApp message via chat-port HTTP API
    #[instrument(skip(self, request), fields(to = request.number))]
    pub async fn send_message(
        &self,
        request: SendMessageRequest,
    ) -> BridgeResult<SendMessageResponse> {
        let url = format!("{}/send", self.config.api_base_url);

        debug!("Sending WhatsApp message to: {}", request.number);

        let response = self
            .http_client
            .post(&url)
            .header("Content-Type", "application/json")
            .json(&request)
            .send()
            .await
            .map_err(BridgeError::Http)?;

        let status = response.status();
        debug!("Chat-port send response status: {}", status);

        if status.is_success() {
            let send_response: SendMessageResponse =
                response.json().await.map_err(BridgeError::Http)?;

            if send_response.success {
                info!(
                    "WhatsApp message sent successfully - Message ID: {:?}",
                    send_response.message_id
                );
            } else {
                warn!("WhatsApp message send failed: {:?}", send_response.error);
            }

            Ok(send_response)
        } else {
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "Unknown error".to_string());

            error!(
                "Chat-port send error - Status: {}, Body: {}",
                status, error_text
            );

            Err(Box::new(BridgeError::ChatPort {
                message: error_text,
            }))
        }
    }

    /// Check if chat-port service is healthy
    #[instrument(skip(self))]
    pub async fn health_check(&self) -> BridgeResult<bool> {
        let url = format!("{}/health", self.config.api_base_url);

        debug!("Checking chat-port health at: {}", url);

        match self
            .http_client
            .get(&url)
            .timeout(Duration::from_secs(10))
            .send()
            .await
        {
            Ok(response) => {
                let is_healthy = response.status().is_success();
                debug!("Chat-port health check result: {}", is_healthy);
                Ok(is_healthy)
            }
            Err(e) => {
                warn!("Chat-port health check failed: {}", e);
                Ok(false)
            }
        }
    }
}
