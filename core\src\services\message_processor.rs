/*!
# Message Processor

Handles message validation, formatting, and routing between WhatsApp and AI services.
*/

use crate::{
    config::BridgeConfig,
    error::{BridgeError, BridgeResult},
    services::{ai_client::AiClient, chat_port_client::ChatPortClient},
    types::{ExplanationRequest, IncomingWhatsAppData, MessageContext, SendMessageRequest},
};
use governor::{Quota, RateLimiter};
use std::{
    collections::HashMap,
    num::NonZeroU32,
    sync::Arc,
    time::{Duration, Instant},
};
use tokio::sync::{Mutex, Semaphore};
use tracing::{debug, error, info, instrument, warn};

/// Message processor with rate limiting and deduplication
#[derive(Debug)]
pub struct MessageProcessor {
    config: BridgeConfig,
    ai_client: Arc<AiClient>,
    chat_port_client: Arc<ChatPortClient>,
    rate_limiter: Arc<
        RateLimiter<
            governor::state::NotKeyed,
            governor::state::InMemoryState,
            governor::clock::QuantaClock,
        >,
    >,
    processing_semaphore: Arc<Semaphore>,
    message_cache: Arc<Mutex<HashMap<String, Instant>>>,
}

impl MessageProcessor {
    /// Create a new message processor
    pub fn new(
        config: BridgeConfig,
        ai_client: Arc<AiClient>,
        chat_port_client: Arc<ChatPortClient>,
    ) -> Self {
        // Create rate limiter (messages per minute)
        let quota = Quota::per_minute(
            NonZeroU32::new(config.rate_limit_per_minute).unwrap_or(NonZeroU32::new(60).unwrap()),
        );
        let rate_limiter = Arc::new(RateLimiter::direct(quota));

        // Create semaphore for concurrent processing limit
        let processing_semaphore = Arc::new(Semaphore::new(config.max_concurrent_messages));

        // Create message cache for deduplication
        let message_cache = Arc::new(Mutex::new(HashMap::new()));

        info!(
            "Message processor initialized - Rate limit: {}/min, Max concurrent: {}",
            config.rate_limit_per_minute, config.max_concurrent_messages
        );

        Self {
            config,
            ai_client,
            chat_port_client,
            rate_limiter,
            processing_semaphore,
            message_cache,
        }
    }

    /// Process incoming WhatsApp message
    #[instrument(skip(self, whatsapp_data), fields(from = whatsapp_data.from, message_id = whatsapp_data.message_id))]
    pub async fn process_message(&self, whatsapp_data: IncomingWhatsAppData) -> BridgeResult<()> {
        // Check rate limit
        if self.rate_limiter.check().is_err() {
            warn!("Rate limit exceeded for message processing");
            return Err(Box::new(BridgeError::RateLimit(
                "Message processing rate limit exceeded".to_string(),
            )));
        }

        // Check for duplicate messages
        if self.config.enable_deduplication && self.is_duplicate_message(&whatsapp_data).await? {
            info!("Duplicate message detected, skipping processing");
            return Ok(());
        }

        // Acquire processing semaphore
        let _permit =
            self.processing_semaphore.acquire().await.map_err(|_| {
                BridgeError::ServiceUnavailable("Processing queue full".to_string())
            })?;

        // Create message context for tracking
        let mut context = MessageContext::new(&whatsapp_data);
        context.start_processing();

        info!(
            "Processing message {} from {} (ID: {})",
            context.id, context.whatsapp_from, context.whatsapp_message_id
        );

        // Process with timeout
        let result = tokio::time::timeout(
            self.config.message_timeout(),
            self.process_message_internal(context),
        )
        .await;

        match result {
            Ok(Ok(mut context)) => {
                context.message_sent();
                if let Some(total_time) = context.total_processing_time_ms() {
                    info!(
                        "Message {} processed successfully in {}ms",
                        context.id, total_time
                    );
                }
                Ok(())
            }
            Ok(Err(e)) => {
                error!("Message processing failed: {}", e);
                self.send_error_response(&whatsapp_data, &e).await?;
                Err(e)
            }
            Err(_) => {
                error!("Message processing timeout");
                let timeout_error = BridgeError::Timeout("Message processing timeout".to_string());
                self.send_error_response(&whatsapp_data, &timeout_error)
                    .await?;
                Err(Box::new(timeout_error))
            }
        }
    }

    /// Internal message processing logic
    async fn process_message_internal(
        &self,
        mut context: MessageContext,
    ) -> BridgeResult<MessageContext> {
        // Validate message
        self.validate_message(&context)?;

        // Format message for AI service
        let ai_request = self.format_ai_request(&context)?;

        debug!("Sending request to AI service for message {}", context.id);

        // Get AI response
        let ai_response = self.ai_client.generate_explanation(ai_request).await?;
        context.ai_response_received();

        info!(
            "AI response received for message {} - Model: {}, Tokens: {}",
            context.id, ai_response.model, ai_response.tokens_used
        );

        // Format response for WhatsApp
        let whatsapp_message = self.format_whatsapp_response(&ai_response)?;

        // Send response via chat-port
        let send_request = SendMessageRequest {
            number: context.whatsapp_from.clone(),
            message: whatsapp_message,
        };

        debug!("Sending response to WhatsApp for message {}", context.id);

        let send_response = self.chat_port_client.send_message(send_request).await?;

        if !send_response.success {
            return Err(Box::new(BridgeError::ChatPort {
                message: send_response
                    .error
                    .unwrap_or_else(|| "Unknown send error".to_string()),
            }));
        }

        Ok(context)
    }

    /// Validate incoming message
    fn validate_message(&self, context: &MessageContext) -> BridgeResult<()> {
        if context.original_message.trim().is_empty() {
            return Err(Box::new(BridgeError::InvalidMessage(
                "Empty message content".to_string(),
            )));
        }

        if context.original_message.len() > 4000 {
            return Err(Box::new(BridgeError::InvalidMessage(
                "Message too long (max 4000 characters)".to_string(),
            )));
        }

        if context.whatsapp_from.is_empty() {
            return Err(Box::new(BridgeError::InvalidMessage(
                "Missing sender information".to_string(),
            )));
        }

        Ok(())
    }

    /// Format message for AI service request
    fn format_ai_request(&self, context: &MessageContext) -> BridgeResult<ExplanationRequest> {
        // Add context to the prompt to make it more conversational
        let enhanced_prompt = format!(
            "Please provide a helpful and informative response to this message: \"{}\"",
            context.original_message.trim()
        );

        Ok(ExplanationRequest {
            prompt: enhanced_prompt,
            temperature: self.ai_client.config().default_temperature,
            max_tokens: self.ai_client.config().default_max_tokens,
        })
    }

    /// Format AI response for WhatsApp message
    fn format_whatsapp_response(
        &self,
        ai_response: &crate::types::ExplanationResponse,
    ) -> BridgeResult<String> {
        // Clean up the AI response and make it WhatsApp-friendly
        let content = ai_response.content.trim();

        if content.is_empty() {
            return Ok(
                "I apologize, but I couldn't generate a response at the moment. Please try again."
                    .to_string(),
            );
        }

        // Ensure the message isn't too long for WhatsApp (limit to ~4000 chars)
        if content.len() > 4000 {
            let truncated = &content[..3950];
            Ok(format!(
                "{}...\n\n(Response truncated due to length)",
                truncated
            ))
        } else {
            Ok(content.to_string())
        }
    }

    /// Check if message is a duplicate
    async fn is_duplicate_message(
        &self,
        whatsapp_data: &IncomingWhatsAppData,
    ) -> BridgeResult<bool> {
        let mut cache = self.message_cache.lock().await;
        let now = Instant::now();
        let dedup_window = Duration::from_secs(self.config.deduplication_window_secs);

        // Clean up old entries
        cache.retain(|_, timestamp| now.duration_since(*timestamp) < dedup_window);

        // Create deduplication key (sender + message content hash)
        let dedup_key = format!(
            "{}:{}",
            whatsapp_data.from,
            whatsapp_data.message.chars().take(100).collect::<String>()
        );

        if cache.contains_key(&dedup_key) {
            return Ok(true);
        }

        // Add to cache
        cache.insert(dedup_key, now);
        Ok(false)
    }

    /// Send error response to WhatsApp user
    async fn send_error_response(
        &self,
        whatsapp_data: &IncomingWhatsAppData,
        error: &BridgeError,
    ) -> BridgeResult<()> {
        let error_message = match error {
            BridgeError::RateLimit(_) => {
                "I'm currently handling many requests. Please try again in a few minutes."
                    .to_string()
            }
            BridgeError::Timeout(_) => {
                "Your request is taking longer than expected. Please try again.".to_string()
            }
            BridgeError::InvalidMessage(_) => {
                "I couldn't process your message. Please make sure it's not empty and try again."
                    .to_string()
            }
            BridgeError::ServiceUnavailable(_) => {
                "I'm temporarily unavailable. Please try again later.".to_string()
            }
            _ => "I encountered an error processing your message. Please try again.".to_string(),
        };

        let send_request = SendMessageRequest {
            number: whatsapp_data.from.clone(),
            message: error_message,
        };

        match self.chat_port_client.send_message(send_request).await {
            Ok(_) => debug!("Error response sent to user"),
            Err(e) => warn!("Failed to send error response: {}", e),
        }

        Ok(())
    }

    /// Get processing statistics
    pub async fn get_stats(&self) -> ProcessingStats {
        let cache = self.message_cache.lock().await;
        ProcessingStats {
            cached_messages: cache.len(),
            available_permits: self.processing_semaphore.available_permits(),
            max_concurrent: self.config.max_concurrent_messages,
        }
    }
}

/// Processing statistics
#[derive(Debug, Clone)]
pub struct ProcessingStats {
    pub cached_messages: usize,
    pub available_permits: usize,
    pub max_concurrent: usize,
}
