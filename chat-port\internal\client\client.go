package client

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"chatport-go/config"
	"chatport-go/internal/websocket"

	_ "github.com/mattn/go-sqlite3"
	"github.com/mdp/qrterminal"
	"go.mau.fi/whatsmeow"
	"go.mau.fi/whatsmeow/proto/waE2E"
	"go.mau.fi/whatsmeow/store/sqlstore"
	"go.mau.fi/whatsmeow/types"
	"go.mau.fi/whatsmeow/types/events"
	waLog "go.mau.fi/whatsmeow/util/log"
)

// WhatsAppClient represents the WhatsApp client with WebSocket broadcasting
type WhatsAppClient struct {
	client    *whatsmeow.Client
	container *sqlstore.Container
	logger    waLog.Logger
}

var Client *WhatsAppClient

// InitClient initializes the WhatsApp client with proper error handling and logging
func InitClient() error {
	config.LoadEnv()

	// Create logger
	logger := waLog.Stdout("Database", "DEBUG", true)

	// Create context
	ctx := context.Background()

	// Initialize database container
	dbPath := config.GetEnv("WHATSAPP_DB_PATH", "file:session.db?_foreign_keys=on")
	container, err := sqlstore.New(ctx, "sqlite3", dbPath, logger)
	if err != nil {
		return fmt.Errorf("failed to create database container: %w", err)
	}

	// Get device store
	deviceStore, err := container.GetFirstDevice(ctx)
	if err != nil {
		return fmt.Errorf("failed to get device store: %w", err)
	}

	// Create WhatsApp client
	whatsappClient := whatsmeow.NewClient(deviceStore, logger)

	Client = &WhatsAppClient{
		client:    whatsappClient,
		container: container,
		logger:    logger,
	}

	// Set up event handlers
	Client.setupEventHandlers()

	// Connect to WhatsApp
	if err := Client.connect(); err != nil {
		return fmt.Errorf("failed to connect to WhatsApp: %w", err)
	}

	// Set up graceful shutdown
	Client.setupGracefulShutdown()

	return nil
}

// connect handles the WhatsApp connection process including QR code display
func (c *WhatsAppClient) connect() error {
	if c.client.Store.ID == nil {
		// Device not logged in, need QR code
		qrChan, err := c.client.GetQRChannel(context.Background())
		if err != nil {
			return fmt.Errorf("failed to get QR channel: %w", err)
		}

		err = c.client.Connect()
		if err != nil {
			return fmt.Errorf("failed to connect: %w", err)
		}

		c.logger.Infof("Please scan the QR code to log in to WhatsApp")

		for evt := range qrChan {
			switch evt.Event {
			case "code":
				c.logger.Infof("QR Code: %s", evt.Code)
				c.showQR(evt.Code)
			case "success":
				c.logger.Infof("Successfully logged in to WhatsApp")
				return nil
			case "timeout":
				return fmt.Errorf("QR code scan timeout")
			case "error":
				return fmt.Errorf("QR code error: %v", evt.Error)
			}
		}
	} else {
		// Device already logged in, just connect
		err := c.client.Connect()
		if err != nil {
			return fmt.Errorf("failed to reconnect: %w", err)
		}
		c.logger.Infof("Reconnected to WhatsApp successfully")
	}

	return nil
}

// setupEventHandlers configures event handlers for incoming messages
func (c *WhatsAppClient) setupEventHandlers() {
	c.client.AddEventHandler(func(evt any) {
		switch v := evt.(type) {
		case *events.Message:
			go c.handleIncomingMessage(v)
		case *events.Receipt:
			c.logger.Debugf("Message receipt: %v", v)
		case *events.Connected:
			c.logger.Infof("WhatsApp connected")
		case *events.Disconnected:
			c.logger.Warnf("WhatsApp disconnected")
		}
	})
}

func (c *WhatsAppClient) showQR(code string) {
	fmt.Println()
	fmt.Println("╔══════════════════════════════════════════════════════════════╗")
	fmt.Println("║                    WHATSAPP QR CODE LOGIN                    ║")
	fmt.Println("╚══════════════════════════════════════════════════════════════╝")
	fmt.Println()
	fmt.Println("Scan this QR code with your WhatsApp app to log in:")
	fmt.Println()
	qrterminal.GenerateHalfBlock(code, qrterminal.L, os.Stdout)
	fmt.Println()
	fmt.Println("📱 WhatsApp > Settings > Linked Devices")
}

// handleIncomingMessage processes incoming WhatsApp messages and broadcasts them to WebSocket clients
func (c *WhatsAppClient) handleIncomingMessage(evt *events.Message) {
	// Skip messages from self or groups for now
	if evt.Info.IsFromMe || evt.Info.IsGroup {
		return
	}

	// Extract message text
	var messageText string
	if evt.Message.GetConversation() != "" {
		messageText = evt.Message.GetConversation()
	} else if evt.Message.GetExtendedTextMessage() != nil {
		messageText = evt.Message.GetExtendedTextMessage().GetText()
	} else {
		c.logger.Debugf("Unsupported message type from %s", evt.Info.Sender.User)
		return
	}

	senderNumber := evt.Info.Sender.User
	c.logger.Infof("Received message from %s: %s", senderNumber, messageText)

	// Broadcast to WebSocket clients
	messageID := evt.Info.ID
	websocket.BroadcastWhatsApp(senderNumber, messageText, messageID)
	c.logger.Debugf("Broadcasted message from %s to WebSocket clients", senderNumber)
}

// SendMessage sends a message to a WhatsApp number
func (c *WhatsAppClient) SendMessage(number, message string) error {
	jid := types.NewJID(number, "s.whatsapp.net")

	msg := &waE2E.Message{
		Conversation: &message,
	}

	_, err := c.client.SendMessage(context.Background(), jid, msg)
	if err != nil {
		return fmt.Errorf("failed to send message to %s: %w", number, err)
	}

	c.logger.Infof("Sent message to %s: %s", number, message)
	return nil
}

// setupGracefulShutdown sets up graceful shutdown handling
func (c *WhatsAppClient) setupGracefulShutdown() {
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, os.Interrupt, syscall.SIGTERM)

	go func() {
		<-sigChan
		c.logger.Infof("Received shutdown signal, disconnecting...")
		c.Disconnect()
		os.Exit(0)
	}()
}

// Disconnect gracefully disconnects the WhatsApp client
func (c *WhatsAppClient) Disconnect() {
	if c.client != nil {
		c.client.Disconnect()
		c.logger.Infof("WhatsApp client disconnected")
	}

	if c.container != nil {
		c.container.Close()
		c.logger.Infof("Database container closed")
	}
}

// IsConnected returns whether the client is connected to WhatsApp
func (c *WhatsAppClient) IsConnected() bool {
	return c.client != nil && c.client.IsConnected()
}

// GetJID returns the JID of the connected WhatsApp account
func (c *WhatsAppClient) GetJID() string {
	if c.client != nil && c.client.Store.ID != nil {
		return c.client.Store.ID.User
	}
	return ""
}
